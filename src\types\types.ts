import { DayActivityType } from "@/utils/context/PackageContext";
//Hotel Interface
export interface Hotel {
  _id: string;
  hotelId: string;
  hotelName: string;
  location: {
    address: string;
    country: string;
    destinationId: string;
    lat: string;
    lon: string;
    state: string;
    _id: string;
  };
  amenities: string[];
  contract: {
    additionalEmail: string;
    businessEmail: string;
    maintainerPhoneNo: number;
  };
  viewPoint: string[];
  __v: number;
}

//Amenity Interface
export interface Amenity {
  amenitiesId: string;
  image: string;
  name: string;
  __v: number;
  _id: string;
}

//Hotel Upload Data Interface
export interface HotelUploadData {
  hotelName: string;
  location: {
    address: string;
    country: string;
    destinationId: string;
    lat: string;
    lon: string;
    state: string;
  };
  image: string;
  viewPoint: string[];
  contract: {
    businessEmail: string;
    additionalEmail: string;
    maintainerPhoneNo: string;
  };
  amenities: string[];
}
export type Period = {
  
    startDate: string,
    endDate: string
  
}

export type PackageHotelType = {
  hotelId: string;
  hotelRoomId: string;
  mealPlan: string;
  noOfNight: number;
  sort: number;
  isAddOn: boolean;
}

export interface DestinationType {
  destinationId: string;
  destinationName: string;
  destinationType: string;
  rankNo: number;
}

export type Destination =  {
  destinationId:string,noOfNight:number
}
export type PackageType = {
  packageId?: string;
  newPackageId?:string;
  packageName: string;
  redeemPoint: number;
  planId: string;
  interestId: string;
  destination: Destination[];
  packageImg:  string[]
  noOfDays: number;
  noOfNight: number;
  noOfAdult: number;
  noOfChild: number;
  offer: number;
  startFrom:string;
  hotel: PackageHotelType[],
  availableHotel: string[];
  vehicle: string[]
  availableVehicle: string[];
  period:Period[]
  story: string;
  inclusion:string[]
  exclusion: string[];
  activity: DayActivityType[]
  availableActivity: string[];
  activityPrice: number;
  additionalFees: number;
  marketingPer: number;
  transPer: number;
  agentCommissionPer: number;
  gstPer: number;
  status:boolean;
  sort:number;
  perRoom:number;
};
export interface HotelsType {
  _id: string;
  hotelId: string;
  hotelName: string;
  location: {
    destinationId: string;
    lat: string;
    lon: string;
    address: string;
    state: string;
    country: string;
    _id: string;
  };
  viewPoint: string[];
  image: string;
  contract: {
    businessEmail: string;
    additionalEmail: string;
    maintainerPhoneNo: number;
    _id: string;
  };
  amenities: string[];
  __v: number;
}

export interface HotelRoom {
  hotelId: string;
  hotelRoomId: string;
  hotelRoomType: string;
  isAc: boolean;
  maxAdult: number;
  maxChild: number;
  maxInf: number;
  mealPlan: MealPlan[];
  roomCapacity: number;
}

export interface MealPlan {
  adultPrice: number;
  childPrice: number;
  endDate: string[];
  gstPer: number;
  hotelId: string;
  hotelMealId: string;
  hotelRoomId: string;
  mealPlan: string;
  roomPrice: number;
  seasonType: string;
  startDate: string[];
  _id: string;
}

// useToast interface
export interface useToastProps {
  success: boolean;
  content: string;
}


export interface ParticipantInfo {
  ageRestriction: number;
  maxParticipants: number;
  minParticipants: number;
}

export interface ActivityType {
  activityId: string;
  dayType: string;
  description: string;
  destinationId: string;
  duration: number;
  image: string;
  isPrivate: boolean;
  level: string;
  name: string;
  participantInfo: ParticipantInfo;
  price: number;
  tags: string[];
}

// Tariff Upload interfaces
export interface TariffUpload {
  tariffId?: string;
  hotelId: string;
  roomId: string;
  filePath: string;
  uploadDate: string;
  status: 'pending' | 'approved' | 'rejected';
  approvalDate?: string;
  approvedBy?: string;
  priceData?: ExtractedTariffData[]; // Updated to use ExtractedTariffData
  notes?: string;
  file?: File; // Optional file object for direct uploads
  roomName?: string; 
}

export interface TariffPriceData {
  mealPlanType: string;
  startDate: string;
  endDate: string;
  roomPrice: number;
  adultPrice?: number;
  childPrice?: number;
}

// New interface for extracted tariff data from extract_tariff.py
export interface ExtractedTariffData {
  // Common fields
  'Season': string;
  'Start Date': string;
  'End Date': string;
  
  // LLM path fields (when use_llm=true)
  'Room Category'?: string;
  'Plan'?: string; // LLM uses 'Plan' instead of 'Meal Plan'
  'Room Price'?: string | number;
  'Adult Price'?: string | number;
  'Child Price'?: string | number;
  
  // Fallback path fields (when use_llm=false)
  'Hotel'?: string;
  'Occupancy'?: string;
  'Meal Plan'?: string; // Fallback uses 'Meal Plan'
  'Price'?: string | number; // Fallback uses 'Price'
}